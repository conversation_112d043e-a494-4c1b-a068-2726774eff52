/* Mobile Design Space Styles */

/* Mobile bottom toolbar scrollbar styling */
.mobile-toolbar::-webkit-scrollbar {
  height: 4px;
}

.mobile-toolbar::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-toolbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.mobile-toolbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* Smooth scrolling for mobile toolbar */
.mobile-toolbar {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile tool button animations */
.mobile-tool-button {
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.mobile-tool-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-tool-button.active {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
}

/* Mobile content panel */
.mobile-content-panel {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999 !important;
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

/* Mobile content panel drag handle */
.mobile-content-panel::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* Mobile canvas adjustments */
@media (max-width: 768px) {
  .design-space-container {
    padding: 8px !important;
  }
  
  .design-space {
    max-width: calc(100vw - 16px);
    max-height: calc(100vh - 200px);
    transform: scale(0.8);
    transform-origin: center center;
  }
  
  /* Hide desktop controls on mobile */
  .desktop-only {
    display: none !important;
  }
  
  /* Adjust element controls for mobile */
  .element-controls {
    transform: scale(0.8);
  }
  
  .element-control-btn {
    width: 28px !important;
    height: 28px !important;
    font-size: 12px !important;
  }
  
  /* Mobile-friendly resize handles */
  .resize-handle {
    width: 16px !important;
    height: 16px !important;
    border-width: 2px !important;
  }
  
  /* Mobile toolbar adjustments */
  .mobile-bottom-toolbar {
    height: 80px;
    padding: 8px 12px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }
  
  /* Ensure mobile content doesn't overflow */
  .mobile-content-body {
    max-height: calc(70vh - 80px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 20px;
  }

  /* Ensure mobile panels are above everything */
  .mobile-content-panel {
    z-index: 9999 !important;
  }

  /* Mobile canvas adjustments */
  .mobile-canvas {
    margin-bottom: 100px;
  }
  
  /* Mobile-specific animations */
  .mobile-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  /* Touch-friendly spacing */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Mobile canvas zoom controls */
  .mobile-zoom-controls {
    position: fixed;
    bottom: 100px;
    right: 16px;
    z-index: 40;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .mobile-zoom-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
  }
  
  .mobile-zoom-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
  
  /* Mobile gesture indicators */
  .mobile-gesture-hint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .mobile-gesture-hint.show {
    opacity: 1;
  }
  
  /* Mobile-specific element selection */
  .mobile-element-selected {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
  }
  
  /* Mobile toolbar backdrop */
  .mobile-toolbar-backdrop {
    background: linear-gradient(
      to top,
      rgba(17, 24, 39, 0.95) 0%,
      rgba(17, 24, 39, 0.8) 50%,
      transparent 100%
    );
    backdrop-filter: blur(10px);
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .design-space {
    transform: scale(0.6);
  }
  
  .mobile-content-panel {
    height: 70vh !important;
    max-height: 400px !important;
  }
  
  .mobile-bottom-toolbar {
    height: 60px;
    padding: 4px 8px;
  }
  
  .mobile-tool-button {
    padding: 8px !important;
    min-width: 50px !important;
  }
  
  .mobile-tool-button span {
    font-size: 10px !important;
  }
}

/* High DPI mobile displays */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .design-space {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  .element-controls {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Dark mode mobile adjustments */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
  .mobile-content-panel {
    background: rgba(17, 24, 39, 0.95);
    color: white;
  }
  
  .mobile-zoom-btn {
    background: rgba(17, 24, 39, 0.9);
    color: white;
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
  .mobile-tool-button:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
  }
  
  .mobile-content-panel:focus-within {
    box-shadow: 0 0 0 2px #8b5cf6;
  }
  
  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .mobile-tool-button,
    .mobile-content-panel,
    .mobile-slide-up {
      animation: none !important;
      transition: none !important;
    }
  }
}
