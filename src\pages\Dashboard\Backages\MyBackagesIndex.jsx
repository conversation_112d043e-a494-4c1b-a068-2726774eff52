import React, { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from 'react-query';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tooltip } from 'primereact/tooltip';
import { TfiTrash } from 'react-icons/tfi';
import { FaRegEye, FaEllipsisV, FaSearch } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Container from '@components/Container';
import axiosInstance from "../../../config/Axios";
import { loadStripe } from '@stripe/stripe-js';
import CreateCardForm from './CreateCardToManagerForm';
import { motion, useAnimation } from 'framer-motion';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';
import { Toast } from 'primereact/toast';
import { ConfirmDialog } from 'primereact/confirmdialog';

const fetchPackage = async () => {
  try {
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('user_id');

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage");
      return;
    }

    const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching package:', error);
    throw error;
  }

};
const PackagesDataTable = () => {
  // State declarations
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [isCardViewOpen, setIsCardViewOpen] = useState(false);
  const [isFlipped, setIsFlipped] = useState(false);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [showNfcAnimation, setShowNfcAnimation] = useState(false);
  const [showCardImage, setShowCardImage] = useState(false);
  const currentUserId = localStorage.getItem('user_id');

  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCardData, setFilteredCardData] = useState([]);

  // Mobile responsiveness states
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);

  // Hooks
  const queryClient = useQueryClient();
  const nfcAnimationControls = useAnimation();
  const { data: packageData, isLoading, isError, error, refetch } = useQuery('packageDetails', fetchPackage);

  // Add selection state and toast ref
  const [selectedCards, setSelectedCards] = useState([]);
  const toastRef = React.useRef(null);
  const [showConfirm, setShowConfirm] = useState(false);
  const [cardsToUnassign, setCardsToUnassign] = useState([]);

  // Function to play simple glow animation when card is viewed
  const playGlowAnimation = useCallback(async () => {
    if (selectedCard) {
      setShowNfcAnimation(true);
      setShowCardImage(false);

      // Start simple glow animation
      await nfcAnimationControls.start({
        opacity: [0, 1, 1, 0.7, 1, 0.5, 1],
        scale: [0.98, 1],
        boxShadow: [
          '0 0 0px rgba(255, 255, 255, 0)',
          '0 0 40px rgba(255, 255, 255, 0.8)',
          '0 0 20px rgba(255, 255, 255, 0.4)',
          '0 0 30px rgba(255, 255, 255, 0.7)',
          '0 0 10px rgba(255, 255, 255, 0.3)',
        ],
        transition: { duration: 3, ease: "easeInOut" }
      });

      // Show card image after animation
      setTimeout(() => {
        setShowCardImage(true);
        setShowNfcAnimation(false);
      }, 3000);
    }
  }, [selectedCard, nfcAnimationControls]);

  // Trigger glow animation when card is viewed
  useEffect(() => {
    if (isCardViewOpen && selectedCard) {
      playGlowAnimation();
    }
  }, [isCardViewOpen, selectedCard, playGlowAnimation]);

  // Mobile detection useEffect
  useEffect(() => {
    const handleResize = () => {
      const mobileView = window.innerWidth < 768;
      setIsMobile(mobileView);
      if (!mobileView) {
        setMobileActionMenuOpen(null); // Close mobile menu when switching to desktop
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Filter cards based on search query
  useEffect(() => {
    const cardData = packageData?.cards || [];
    if (!searchQuery.trim()) {
      setFilteredCardData(cardData);
    } else {
      const filtered = cardData.filter(card => {
        const searchLower = searchQuery.toLowerCase();
        return (
          card.name?.toLowerCase().includes(searchLower) ||
          card.number?.toLowerCase().includes(searchLower)
        );
      });
      setFilteredCardData(filtered);
    }
  }, [packageData?.cards, searchQuery]);

  const rowsOptions = [
    { label: '5 Cards', value: 5 },
    { label: '10 Cards', value: 10 },
    { label: '20 Cards', value: 20 },
    { label: 'All Cards', value: null }
  ];

  const statusStyles = {
    printed: "bg-[#22C55E] text-white rounded-[6px] font-bold text-sm py-2 px-3",
    active: "bg-[#22C55E] text-white rounded-[6px] font-bold text-sm py-2 px-3",
    inactive: "bg-[#dc2626] text-white rounded-[6px] font-bold text-sm py-2 px-3",
    unprinted: "bg-gray-500 text-white rounded-[6px] font-bold text-sm py-2 px-3",
    onProgress: "bg-[#D97706] text-white rounded-[6px] font-bold text-sm py-2 px-3",
    draft: "bg-[#dc2626] text-white rounded-[6px] font-bold text-sm py-2 px-3",
    published: "bg-[#22C55E] text-white rounded-[6px] font-bold text-sm py-2 px-3",
  };

  if (isLoading) return <p className="text-center">Loading...</p>;

  if (isError) {
    if (error.response && error.response.data && error.response.data.error === "Your subscription has expired. Please renew your subscription to continue.") {
      return (
        <Container>
          <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
          {/* Background gradient effect - Animated */}
          <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
            <motion.div
              className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
              style={{
                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
              }}
              animate={{
                background: [
                  'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                  'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                  'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
                ],
                rotate: -360,
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear",
              }}
            />
          </div>

            <div className="mx-auto max-w-4xl text-center">
              <h2 className="text-base font-semibold leading-7 text-indigo-600">Subscription Status</h2>
              <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                Subscription Expired
              </h1>
              <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                {error.response.data.error}
              </p>
              <p className="mt-4 text-center text-gray-600">
                Please renew your subscription to continue using our services.
              </p>

            </div>

            {/* Expired package visualization */}
            <div className="mt-16 mx-auto max-w-2xl">
              <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-red-50 to-white"
                style={{ borderTop: '6px solid #ef4444' }}>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Expired Plan</h3>
                <div className="flex items-center">
                  <svg className="h-5 w-5 flex-none text-red-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                  </svg>
                  <span className="ml-3 text-red-600 font-medium">Your subscription has expired</span>
                </div>
                <div className="mt-6 p-3 rounded-lg bg-red-100 text-red-800 flex items-center justify-center">
                  <span className="font-medium">⚠️ Please renew your subscription to continue</span>
                </div>
                <div className="mt-10 flex items-center justify-center gap-x-6">
                <button
                  onClick={() => window.location.href = `${import.meta.env.VITE_ENV}/manager/Packages`}
                  className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                >
                  Renew Now
                </button>

              </div>
              </div>
            </div>
          </div>
        </Container>
      );
    }




    return (
      <Container>
        <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
          {/* Background gradient effect */}
          <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
            <div
              className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
              style={{
                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
                background: 'linear-gradient(45deg, #ff2222 0%, #ff9922 100%)'
              }}
            />
          </div>

          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-base font-semibold leading-7 text-red-600">Error</h2>
            <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
              Something went wrong
            </h1>
            <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
              {error.message}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <button
                onClick={() => window.location.reload()}
                className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Try again
              </button>
              <a href="/" className="text-sm font-semibold leading-6 text-gray-900">
                Go back home <span aria-hidden="true">→</span>
              </a>
            </div>
          </div>
        </div>
      </Container>
    );
  }



  const actionsBodyTemplate = (rowData) => {
    return (
      <div className="flex justify-around space-x-3">
        <Tooltip target=".eye-icon" content="View Card" position="top" />
        <button
          className="eye-icon text-blue-500 hover:text-blue-700 transition duration-200"
          onClick={() => {
            setSelectedCard(rowData);
            setIsCardViewOpen(true);
          }}
        >
          <FaRegEye size={20} />
        </button>




      </div>
    );
  };

  const printedStatusBodyTemplate = (rowData) => {
    const status = rowData.print_status || "unprinted";
    const backgroundColor = status === "unprinted" ? "#9ca3af" :
                          status === "printed" ? "#22C55E" :
                          status === "inactive" ? "#dc2626" : "#9ca3af";

    return (
        <span
            className="inline-block text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize"
            style={{ backgroundColor, width: "100px", textAlign: "center" }}
        >
            {status}
        </span>
    );
};


  const handleDelete = async (cardId) => {
    try {
      await axiosInstance.delete(`/cards/${cardId}`);
      refetch();
    } catch (error) {
      console.error("Error deleting card:", error);
    }
  };

  const render3DCard = () => {
    if (!selectedCard) return null;

    return (
      <div className="flex flex-col items-center justify-center p-6">
        <motion.div
          className="relative"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          onClick={() => setIsFlipped(!isFlipped)}
          style={{ perspective: '1200px' }}
        >
          {/* 3D Card Container */}
          <motion.div
            className="relative cursor-pointer"
            style={{
              transformStyle: 'preserve-3d',
              perspective: '1200px',
              width: 'fit-content',
              transformOrigin: 'center center',
            }}
            animate={{
              rotateY: isFlipped ? 180 : 0,
              rotateX: isFlipped ? 0 : [0, -2, 2, 0],
            }}
            transition={{
              rotateY: { duration: 0.8, ease: "easeInOut" },
              rotateX: {
                duration: 6,
                ease: "easeInOut",
                repeat: Infinity,
                repeatType: "mirror"
              }
            }}
            whileHover={{ scale: 1.02 }}
          >
            {/* Simple Glow Animation Overlay */}
            {showNfcAnimation && (
              <motion.div
                className="absolute inset-0 z-10 rounded-xl overflow-hidden flex items-center justify-center"
                style={{
                  background: 'linear-gradient(145deg, #1a2a3a, #2a3a4a)',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  width: '300px',
                  height: '400px',
                  minWidth: '300px',
                  minHeight: '400px',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)'
                }}
                animate={nfcAnimationControls}
              >
                {/* Card Glow Effect */}
                <motion.div
                  className="absolute inset-0 bg-white opacity-0"
                  animate={{
                    opacity: [0, 0.3, 0.1, 0.4, 0.2, 0.5, 0],
                  }}
                  transition={{
                    duration: 3,
                    ease: "easeInOut",
                  }}
                />

                {/* Connection Type Icon with Glow Effect */}
                <motion.div
                  className="relative"
                  animate={{
                    scale: [0.9, 1.1, 1],
                    opacity: [0.5, 1, 0.8],
                  }}
                  transition={{
                    duration: 3,
                    ease: "easeInOut",
                  }}
                  style={{
                    filter: "drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))"
                  }}
                >
                  {selectedCard?.card_type?.type_of_connection === 'NFC' ? (
                    // NFC Icon
                    <svg className="w-32 h-32 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <motion.path
                        d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                          pathLength: [0, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse"
                        }}
                      />
                      <motion.path
                        d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                          pathLength: [0, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse",
                          delay: 0.2
                        }}
                      />
                      <motion.path
                        d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                          pathLength: [0, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse",
                          delay: 0.4
                        }}
                      />
                      <motion.path
                        d="M20 4L20 8"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                          pathLength: [0, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse",
                          delay: 0.6
                        }}
                      />
                      <motion.path
                        d="M20 4L16 4"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                          pathLength: [0, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse",
                          delay: 0.8
                        }}
                      />
                    </svg>
                  ) : selectedCard?.card_type?.type_of_connection === 'Bluetooth' ? (
                    // Bluetooth Icon
                    <svg className="w-32 h-32 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <motion.path
                        d="M6 8L18 16L12 22V2L18 8L6 16"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                          pathLength: [0, 1]
                        }}
                        transition={{
                          duration: 1
                        }}
                      />
                    </svg>
                  ) : (
                    // Default Card Icon
                    <svg className="w-32 h-32 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <motion.rect
                        x="2" y="4"
                        width="20" height="16"
                        rx="2"
                        stroke="currentColor"
                        strokeWidth="2"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse"
                        }}
                      />
                      <motion.path
                        d="M2 10H22"
                        stroke="currentColor"
                        strokeWidth="2"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse",
                          delay: 0.2
                        }}
                      />
                      <motion.path
                        d="M6 16H10"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        animate={{
                          strokeOpacity: [0.5, 1, 0.7, 1],
                        }}
                        transition={{
                          duration: 3,
                          repeat: 1,
                          repeatType: "reverse",
                          delay: 0.4
                        }}
                      />
                    </svg>
                  )}
                </motion.div>

                {/* Light Rays */}
                <motion.div
                  className="absolute inset-0"
                  animate={{
                    background: [
                      'radial-gradient(circle, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 100%)',
                      'radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%)',
                      'radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%)',
                      'radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 80%)',
                      'radial-gradient(circle, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 100%)',
                    ]
                  }}
                  transition={{
                    duration: 3,
                    ease: "easeInOut",
                  }}
                />
              </motion.div>
            )}

            {/* Card Front */}
            <motion.div
              className="flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
              style={{
                backfaceVisibility: 'hidden',
                background: 'linear-gradient(145deg, #1a2a3a, #2a3a4a)',
                border: '2px solid rgba(255, 255, 255, 0.1)',
                width: 'fit-content',
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2), 0 0 15px rgba(59, 130, 246, 0.5)',
              }}
            >
              {/* 3D Depth Effect */}
              <div className="absolute inset-0" style={{
                background: 'linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',
                transform: 'translateZ(2px)',
                pointerEvents: 'none'
              }}></div>

              {/* Glass Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>

              {/* Glowing Effect */}
              <div className="absolute inset-0 rounded-xl overflow-hidden">
                <div className="absolute -top-10 -left-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
                <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
              </div>

              {/* Card Content */}
              <div className="relative p-4 flex flex-col items-center max-w-sm mx-auto">
                {/* Main Image - Fixed size to match animation */}
                <div className="relative overflow-hidden" style={{ width: '300px', height: '400px' }}>
                  {selectedCard.image_path && showCardImage ? (
                    <motion.img
                      src={selectedCard.image_path}
                      alt="Card"
                      className="object-contain w-full h-full"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.8 }}
                      whileHover={{ scale: 1.05 }}
                    />
                  ) : !showNfcAnimation && !showCardImage ? (
                    <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                      <span className="text-gray-300">Loading...</span>
                    </div>
                  ) : null}

                  {/* Glass effect overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white opacity-10"></div>

                  {/* Floating particles effect */}
                  <div className="absolute inset-0 overflow-hidden">
                    {[...Array(15)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute bg-white rounded-full"
                        style={{
                          width: `${Math.random() * 3 + 1}px`,
                          height: `${Math.random() * 3 + 1}px`,
                          left: `${Math.random() * 100}%`,
                          top: `${Math.random() * 100}%`,
                          opacity: Math.random() * 0.3,
                        }}
                        animate={{
                          y: [0, (Math.random() - 0.5) * 20],
                          x: [0, (Math.random() - 0.5) * 20],
                        }}
                        transition={{
                          duration: Math.random() * 5 + 3,
                          repeat: Infinity,
                          repeatType: "reverse",
                        }}
                      />
                    ))}
                  </div>
                </div>

                {/* Shine effect on hover */}
                <motion.div
                  className="absolute inset-0 bg-white opacity-0 pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: isFlipped ? 0 : [0, 0.1, 0],
                    x: [-100, 300],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    repeatDelay: 3,
                  }}
                  style={{
                    background: 'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%)',
                  }}
                />
              </div>
            </motion.div>

            {/* Card Back */}
            <motion.div
            className="absolute top-0 left-0 flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
            style={{
              backfaceVisibility: 'hidden',
              background: 'linear-gradient(145deg, #2a3a4a, #1a2a3a)',
              border: '2px solid rgba(255, 255, 255, 0.1)',
              transform: 'rotateY(180deg)',
              width: '100%',
              height: '100%',
              minWidth: '300px',
              minHeight: '400px',
            }}
          >
              {/* Glass Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>

              {/* Glowing Effect */}
              <div className="absolute inset-0 rounded-xl overflow-hidden">
                <div className="absolute -top-10 -left-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
                <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
              </div>

              {/* Back Content */}
              <div className="relative w-full h-full p-6 flex flex-col justify-between">
              {/* Magnetic Strip */}
              <motion.div
                className="w-full h-8 bg-gradient-to-r from-black to-gray-800 rounded-sm mb-4"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              />

              {/* Barcode Placeholder */}
              <motion.div
                className="w-full h-16 bg-white bg-opacity-10 rounded-sm flex items-center justify-center mb-4"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
              >
                <div className="w-full h-12 bg-gray-800 rounded-sm flex items-center justify-center">
                  <span className="text-xs text-gray-400">card information</span>
                </div>
              </motion.div>

                {/* Information Section */}
                <motion.div
                  className="w-full space-y-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  <div className="flex justify-between">
                    <span className="text-xs text-blue-200">Member Name :</span>
                    <span className="text-xs text-white">{selectedCard.member_name || 'Member Name'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-xs text-blue-200">Member Type :</span>
                    <span className="text-xs text-white">{selectedCard.member_type || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-xs text-blue-200">Serial No:</span>
                    <span className="text-xs text-white">{selectedCard.number || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-xs text-blue-200">Number of colors:</span>
                    <span className="text-xs text-white">{selectedCard.card_type?.number_of_colors ?? 'N/A'}</span>
                  </div>
                </motion.div>

                {/* Signature Area */}
                <motion.div
                  className="mt-4 pt-2 border-t border-gray-700"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-xs text-blue-200 mb-1">Type Of Connection</p>
                      <div className="h-8 w-24 bg-gray-700  rounded-sm"></div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-blue-200">Connection</p>
                      <p className="text-xs text-white flex items-center justify-end">
                        {selectedCard?.card_type?.type_of_connection === 'NFC' && (
                          <svg className="w-5 h-5 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                            <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                            <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                            <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                            <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                          </svg>
                        )}
                        {selectedCard?.card_type?.type_of_connection === 'Bluetooth' && (
                          <svg className="w-5 h-5 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        )}
                        {selectedCard?.card_type?.type_of_connection || 'N/A'}
                      </p>
                    </div>
                  </div>
                </motion.div>

                {/* Footer */}
                <motion.div
                  className="w-full text-center mt-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  <p className="text-[8px] text-blue-300 opacity-70">
                    This badge is property of the card owner. If found, please return to nearest office.
                  </p>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>

        <div className="mt-28 text-center space-y-4">
          <motion.p
            className="text-sm text-gray-500"
            animate={{
              opacity: [0.6, 1, 0.6],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
            }}
          >
            Click to flip
          </motion.p>
          <div className="flex space-x-4 justify-center">
            <motion.button
              onClick={() => setIsFlipped(!isFlipped)}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:opacity-90 transition shadow-md"
              whileHover={{ scale: 1.05, boxShadow: "0px 0px 15px rgba(99, 102, 241, 0.5)" }}
              whileTap={{ scale: 0.95 }}
            >
              Flip Card
            </motion.button>
          </div>
        </div>
      </div>
    );
  };

  // Mobile list view component
  const MobileListView = () => {
    if (isLoading) {
      return (
        <div className="space-y-2">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="bg-white border rounded-lg p-4 shadow-sm animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex items-center flex-1">
                  <div className="w-12 h-12 bg-gray-300 rounded-lg mr-3"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                  </div>
                </div>
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (isError) {
      return (
        <div className="text-center py-8">
          <p className="text-red-500">Error loading cards</p>
        </div>
      );
    }

    if (filteredCardData.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No cards found</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {filteredCardData.map((card, index) => (
          <div key={card.id || index} className="bg-white border rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center flex-1">
                {/* Card Icon */}
                <div className="w-12 h-12 bg-blue-100 rounded-lg mr-3 flex items-center justify-center">
                  {card?.card_type?.type_of_connection === 'NFC' ? (
                    <svg className="w-6 h-6 text-blue-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                  ) : card?.card_type?.type_of_connection === 'Bluetooth' ? (
                    <svg className="w-6 h-6 text-blue-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  ) : (
                    <svg className="w-6 h-6 text-blue-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="2" y="4" width="20" height="16" rx="2" stroke="currentColor" strokeWidth="2"/>
                      <path d="M2 10H22" stroke="currentColor" strokeWidth="2"/>
                      <path d="M6 16H10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                  )}
                </div>

                {/* Card Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 truncate">{card.name.length > 20
                    ? card.name.slice(0, 20) + '...'
                    : card.name}</h3>
                  <p className="text-sm text-gray-500 truncate">#{card.number}</p>
                  <p className="text-sm text-gray-500 truncate">{card.member_name}</p>
                  <div className="flex items-center mt-1 space-x-2">
                    <span className="text-xs text-gray-400">{card.card_type?.name}</span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        card.print_status === 'printed' ? 'bg-green-100 text-green-800' :
                        card.print_status === 'active' ? 'bg-green-100 text-green-800' :
                        card.print_status === 'inactive' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {card.print_status || 'unprinted'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Mobile Action Menu */}
              <div className="relative">
                <button
                  onClick={() => setMobileActionMenuOpen(mobileActionMenuOpen === card.id ? null : card.id)}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <FaEllipsisV size={16} />
                </button>

                {mobileActionMenuOpen === card.id && (
                  <div className="absolute right-0 top-8 bg-white border rounded-lg shadow-lg z-10 min-w-[120px]">
                    <button
                      onClick={() => {
                        setSelectedCard(card);
                        setIsCardViewOpen(true);
                        setMobileActionMenuOpen(null);
                      }}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
                    >
                      <FaRegEye className="mr-2" size={14} />
                      View Card
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Unassign API call
  const unassignCards = async (cardIds) => {
    try {
      const token = localStorage.getItem('token');
      
      // Get the full card data for the cards we want to unassign
      const cardsToUnassign = filteredCardData.filter(card => cardIds.includes(card.id));
      
      // Prepare the batch request data
      const batchData = cardsToUnassign.map(card => ({
        card_number: card.number,
        user_id: card.user_id
      }));

      // Send all unassign requests in parallel
      await Promise.all(batchData.map(data =>
        axiosInstance.post('/cards/unassign', data, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ));
      
      toastRef.current.show({
        severity: 'success',
        summary: 'Success',
        detail: cardIds.length > 1 
          ? `${cardIds.length} cards have been unassigned successfully` 
          : 'Card has been unassigned successfully',
        life: 3000
      });
      setSelectedCards([]);
      refetch();
    } catch (error) {
      console.error('Error unassigning cards:', error.response?.data);
      toastRef.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.response?.data?.message || 
          (cardIds.length > 1 
            ? 'Failed to unassign cards' 
            : 'Failed to unassign card'),
        life: 3000
      });
    }
  };

  // Single unassign handler
  const handleUnassign = (cardId) => {
    setCardsToUnassign([cardId]);
    setShowConfirm(true);
  };

  // Batch unassign handler
  const handleBatchUnassign = () => {
    const selectedCardIds = selectedCards.map(card => card.id);
    setCardsToUnassign(selectedCardIds);
    setShowConfirm(true);
  };

  // Check if any selected cards are assigned to users (not "No Members")
  const hasAssignedCards = selectedCards.some(card => 
    card.member_name && 
    card.member_name !== "No Members" && 
    card.user_id !== currentUserId
  );

  // Confirm dialog accept
  const onConfirmUnassign = () => {
    setShowConfirm(false);
    if (cardsToUnassign.length > 0) {
      unassignCards(cardsToUnassign);
    }
  };

  // Confirm dialog reject
  const onRejectUnassign = () => {
    setShowConfirm(false);
    setCardsToUnassign([]);
  };

  return (
    <Container>
      <div className="w-full flex justify-between mb-4">
        <h1 className="text-2xl font-semibold text-gray-700">Associated Cards</h1>
      </div>

      {/* Search Bar Section */}
      <div className={`w-full mb-4 mt-1 ${isMobile ? 'px-2' : 'flex justify-center items-center'}`}>
        <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search by card name or number..."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <CreateCardForm
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        fetchPackages={refetch}
        packageData={packageData}
      />

      {/* Conditional rendering for mobile vs desktop */}
      <div className="flex-grow h-full">
        {isMobile ? (
          // Mobile view
          <div className="h-full overflow-y-auto">
            <MobileListView />
          </div>
        ) : (
          // Desktop view
          <>
            {/* Batch Unassign Button */}
            {selectedCards.length > 0 && hasAssignedCards && (
              <button
                className="mb-3 px-6 py-2 rounded-xl font-bold text-sm transition-all duration-500 ease-out transform hover:scale-105 hover:-translate-y-1 active:scale-95 flex items-center justify-center border-0 outline-none focus:outline-none bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-lg"
                onClick={handleBatchUnassign}
              >
                <i className="pi pi-trash mr-2"></i>
                Unassign Selected ({selectedCards.filter(card => 
                  card.member_name && 
                  card.member_name !== "No Members" && 
                  card.user_id !== currentUserId
                ).length})
              </button>
            )}
            <DataTable
              value={filteredCardData}
              paginator
              rows={rowsPerPage}
              rowsPerPageOptions={[5, 25, 50, 100]}
              onRowsChange={(e) => setRowsPerPage(e.value)}
              responsiveLayout="scroll"
              scrollable
              scrollHeight="100%"
              selection={selectedCards}
              onSelectionChange={e => setSelectedCards(e.value)}
              dataKey="id"
            >
              <Column selectionMode="multiple" headerStyle={{ width: '3em' }} />
              <Column sortable field="name" header="Card Name" className="text-left" />
              <Column sortable field="number" header="Card Number" className="text-left" />
              <Column sortable field="card_type.name" header="Card Type" className="text-center" />
              <Column sortable field="member_name" header="Member Name" className="text-left" />
              <Column
                body={(rowData) => (
                  <div className="flex items-center justify-center w-full">
                    {rowData?.card_type?.type_of_connection === 'NFC' && (
                      <svg className="w-5 h-5 text-blue-500 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      </svg>
                    )}
                    {rowData?.card_type?.type_of_connection === 'Bluetooth' && (
                      <svg className="w-5 h-5 text-blue-500 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                    <span className="text-center">{rowData?.card_type?.type_of_connection || 'N/A'}</span>
                  </div>
                )}
                header="Type Of Connection"
                className="text-center"
                align="center"
              />
              {/* Unassign Card Column - left of status */}
              <Column
                header="Unassign Card"
                body={(rowData) => {
                  const isAssignedToOtherUser = rowData.member_name && 
                    rowData.member_name !== "No Members" && 
                    rowData.user_id !== currentUserId;
                  return isAssignedToOtherUser ? (
                    <button
                      className="px-4 py-2 rounded-lg font-bold text-sm bg-gradient-to-r from-red-500 to-orange-500 text-white shadow hover:scale-105 transition-all duration-300 flex items-center justify-center"
                      onClick={() => handleUnassign(rowData.id)}
                      title="Unassign Card"
                    >
                      <i className="pi pi-trash mr-2"></i>
                      Unassign
                    </button>
                  ) : (
                    <span className="text-gray-400 italic">
                      {rowData.member_name === "No Members" ? 'Not assigned' : 
                       rowData.member_name ? 'Assigned to you' : 'Not assigned'}
                    </span>
                  );
                }}
                className="text-center"
                style={{ minWidth: '150px' }}
              />
              <Column sortable body={printedStatusBodyTemplate} field="print_status" header="Print Status" className='text-center' />
              <Column body={actionsBodyTemplate} header="Actions" className="text-center" />
            </DataTable>
            <Toast ref={toastRef} />
            <ConfirmDialog
              visible={showConfirm}
              onHide={onRejectUnassign}
              message={`Are you sure you want to unassign ${cardsToUnassign.length > 1 ? 'these cards' : 'this card'}?`}
              header="Unassign Card Confirmation"
              icon="pi pi-exclamation-triangle"
              accept={onConfirmUnassign}
              reject={onRejectUnassign}
              acceptLabel="Yes, Unassign"
              rejectLabel="Cancel"
            />
          </>
        )}
      </div>

        {/* Professional Card View Dialog */}
      <Dialog
        header="Card Preview"
        visible={isCardViewOpen}
        style={{
          width: isMobile ? '95vw' : '50vw',
          maxWidth: isMobile ? '95vw' : '800px',
          height: isMobile ? '90vh' : 'auto'
        }}
        breakpoints={{
          '960px': '95vw',
          '641px': '95vw'
        }}
        onHide={() => {
          setIsCardViewOpen(false);
          setIsFlipped(false);
        }}
        className={`badge-view-dialog ${isMobile ? 'mobile-card-dialog' : ''}`}
        contentStyle={{
          height: isMobile ? 'calc(90vh - 60px)' : 'auto',
          overflow: isMobile ? 'auto' : 'visible',
          padding: isMobile ? '10px' : '20px'
        }}
      >
        {render3DCard()}
      </Dialog>
  </Container>
  );
};

export default PackagesDataTable;