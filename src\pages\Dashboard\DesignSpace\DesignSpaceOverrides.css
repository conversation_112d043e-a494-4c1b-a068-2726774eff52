/* Override styles to remove transparency from design space */

.design-space {
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: transform 0.3s ease, box-shadow 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  transform-style: preserve-3d;
  perspective: 1200px;
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  z-index: 10 !important;
  transform-origin: center center !important;
  will-change: transform;
}

/* Hide design elements from the design area */
.design-space-container {
  position: relative;
  z-index: 5;
}

.design-elements-container {
  z-index: 1;
  pointer-events: none;
}

/* Ensure design elements don't appear inside the design space */
.design-space .design-element {
  display: none !important;
}

/* Smart positioning for element controls */
.draggable-element.top-edge .element-controls {
  top: auto !important;
  bottom: -55px !important;
}

.draggable-element.top-edge .element-controls::after {
  bottom: auto !important;
  top: -6px !important;
}

.draggable-element.right-edge .element-controls {
  right: auto !important;
  left: -10px !important;
}

.draggable-element.right-edge .element-controls::after {
  right: auto !important;
  left: 15px !important;
}



/* Keep shadow but make it more solid */

/* Remove hover effects that change transparency */


/* Extra force: Remove any scale/transform from libraries (framer-motion, Tailwind, etc) on hover when zoom is not 100% */
.design-space.no-hover-zoom:hover, .design-space.no-hover-zoom.motion-hover, .design-space.no-hover-zoom:hover, .design-space.no-hover-zoom:active {
  transform: none !important;
  scale: 1 !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: none !important;
  filter: none !important;
}

/* Make corner marks more visible */
.design-space .corner-mark {
  border-color: rgba(0, 0, 0, 0.4) !important;
}

/* Remove forced white background */
.design-space > div {
  opacity: 1 !important;
}

/* Zoom functionality styles */
.design-space[data-design-space="true"] {
  transform-origin: center center !important;
  will-change: transform !important;
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
}

/* Ensure zoom container allows overflow for zoomed content */
.design-space-container {
  overflow: auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 100% !important;
  padding: 50px !important;
}

/* Smooth zoom transitions */
.design-space.zooming {
  transition: transform 0.3s ease !important;
}

.element-controls {
  transform: scale(0.8);
  gap: 2px;
  font-size: 12px;
  padding: 2px 6px;
}

.element-controls:hover {
  transform: none !important;
  margin: 0 !important;
  top: initial !important;
  bottom: initial !important;
  left: initial !important;
  right: initial !important;
}

.element-control-btn {
  width: 22px !important;
  height: 22px !important;
  min-width: 0 !important;
  min-height: 0 !important;
  font-size: 11px !important;
  padding: 0 !important;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s !important;
}

.element-controls .element-control-btn:hover, .element-controls .element-control-btn:focus {
  background: #06b6d4 !important;
  color: #fff !important;
  box-shadow: 0 2px 8px #06b6d455 !important;
}